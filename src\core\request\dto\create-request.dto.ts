import { AutoMap } from '@automapper/classes';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for creating a new request
 *
 * When creating a church request (isChurch: true), a letter file must be uploaded
 * using the 'letter' field in multipart/form-data.
 *
 * Example usage:
 * - For ministry request: Send JSON data only
 * - For church request: Send multipart/form-data with JSON data + letter file
 */
export class CreateRequestDto {
  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The name of the requester',
    example: '<PERSON>',
    name: 'requesterName',
  })
  @IsString()
  requesterName: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The email of the requester',
    name: 'requesterEmail',
    example: '<EMAIL>',
  })
  @IsEmail()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The phone number of the requester',
    name: 'requesterPhone',
    example: '+1234567890',
  })
  @IsString()
  requesterPhone: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates if the requester is from a ministry',
    name: 'isMinistry',
  })
  @IsBoolean()
  @IsOptional()
  isMinistry: boolean;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The name of the ministry if applicable',
    name: 'ministryName',
    example: 'Youth Ministry',
  })
  @IsString()
  @IsOptional()
  ministryName: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates if the requester is from a church',
    name: 'isChurch',
  })
  @IsBoolean()
  @IsOptional()
  isChurch: boolean;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The name of the church if applicable',
    name: 'churchName',
    example: "St. Mary's Church",
  })
  @IsString()
  @IsOptional()
  churchName: string;

  @AutoMap()
  @ApiProperty({
    type: Number,
    description: "The ID of the requester's department",
    name: 'requesterDepartmentId',
    example: 12,
    required: true,
  })
  @IsNumber()
  requesterDepartmentId: number;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: "The name of the requester's Head of Department",
    name: 'requesterHodName',
    example: 'John Smith',
  })
  @IsString()
  locationOfUse: string;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'The date when items are requested are to be collected',
    example: '2023-10-01T12:00:00Z',
    name: 'dateOfReturn',
  })
  @IsString()
  dateOfCollection: Date;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'The date when items requested are to be returned',
    example: '2023-10-01T12:00:00Z',
    name: 'dateOfReturn',
  })
  @IsString()
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The reason for the request',
    name: 'descriptionOfRequest',
    example: 'Requesting items for an upcoming event',
  })
  @IsString()
  descriptionOfRequest: string;

  // @AutoMap()
  // @ApiProperty({
  //   type: 'object',
  //   description:
  //     'Church letter details (url and publicId) - populated after file upload',
  //   name: 'churchLetter',
  //   example: {
  //     url: 'https://res.cloudinary.com/.../church-letters/...',
  //     publicId: 'church-letters/...',
  //   },
  //   required: false,
  // })
  // @IsOptional()
  // churchLetter?: ChurchLetter;

  @AutoMap(() => CreateItemAuditDto)
  @ApiProperty({ type: CreateItemAuditDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateItemAuditDto)
  items: Array<CreateItemAuditDto>;
}
