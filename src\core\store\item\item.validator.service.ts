import { Item } from './entities/item.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseValidator } from '@common/validator/base.validator';
import { DatabaseAction } from '@common/enums/dbaction.enum';

@Injectable()
export class ItemValidatorService implements BaseValidator<Item> {
  constructor(
    @InjectRepository(Item) private itemRepository: Repository<Item>,
  ) {}

  async validate(data: Item, action: DatabaseAction) {
    const existing: Item = await this.itemRepository.findOne({
      where: { id: data?.id },
    });

    if (DatabaseAction.CREATE === action) {
      return;
    } else if (
      existing &&
      existing?.id !== data?.id &&
      DatabaseAction.UPDATE === action
    ) {
      throw new NotFoundException('Item not found.');
    }
  }

  async isItemExists(itemId: number): Promise<boolean> {
    const existing: Item = await this.itemRepository.findOne({
      where: { id: itemId },
    });

    return !!existing;
  }
}
