import { Column, Entity, OneToMany } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { IsEmail } from 'class-validator';
import { AbstractEntity } from '@common/entities/base.entity';
import { Item } from '@core/store/item/entities/item.entity';

@Entity('department')
export class Department extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar' })
  name: string;

  @AutoMap()
  @Column({ name: 'hod_name', type: 'varchar', nullable: true })
  hodName: string;

  @AutoMap()
  @Column({ name: 'hod_phone', type: 'varchar', nullable: true })
  hodPhone: string;

  @AutoMap()
  @IsEmail()
  @Column({ name: 'hod_email', type: 'varchar', nullable: true })
  hodEmail: string;

  @AutoMap(() => Item)
  @OneToMany(() => Item, (item) => item.department, {
    cascade: true,
  })
  items: Item[];
}
