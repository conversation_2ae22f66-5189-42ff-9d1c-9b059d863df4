import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsString, IsInt, IsPositive, IsOptional } from 'class-validator';

export class CreateItemDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty({
    type: Number,
    default: 0,
    name: 'actualQuantity',
    description: 'The quantity of the item that is available for use or sale.',
  })
  @IsNumber()
  @Transform(({ value }) => {
    const num = Number(value);
    if (isNaN(num)) {
      throw new Error('actualQuantity must be a valid number');
    }
    return num;
  })
  actualQuantity: number;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  @IsBoolean()
  fragile: boolean;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  @IsPositive()
  @IsInt()
  @Transform(({ value }) => {
    const num = Number(value);
    if (isNaN(num) || !Number.isInteger(num) || num <= 0) {
      throw new Error('departmentId must be a positive integer');
    }
    return num;
  })
  departmentId: number;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The condition of the item units',
    example: 'Good',
    required: false,
  })
  @IsString()
  @IsOptional()
  condition?: string;

  @AutoMap(() => Number)
  @ApiProperty({
    type: Number,
    description: 'The store ID where the item units will be stored',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsInt()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null) return undefined;
    const num = Number(value);
    if (isNaN(num) || !Number.isInteger(num) || num <= 0) {
      throw new Error('storeId must be a positive integer');
    }
    return num;
  })
  storeId?: number;
}
