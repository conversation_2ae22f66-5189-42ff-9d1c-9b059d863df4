import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { RequestStatus } from '@common/enums/request-status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail } from 'class-validator';
import { ChurchLetter } from './type/church-letter.type';

export class RequestTableDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  requesterName: string;

  @AutoMap()
  @ApiProperty()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty()
  requesterDepartment: string;

  @AutoMap()
  @ApiProperty()
  requesterHodName: string;

  @AutoMap()
  @IsEmail()
  @ApiProperty()
  requesterHodEmail: string;

  @AutoMap()
  @ApiProperty()
  ministryName: string;

  @AutoMap()
  @ApiProperty()
  durationOfUse: Date;

  @AutoMap()
  @ApiProperty()
  dateOfCollection: Date;

  @AutoMap()
  @ApiProperty()
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty({ enum: RequestStatus })
  requestStatus: RequestStatus;

  @AutoMap()
  @ApiProperty({
    type: 'object',
    description: 'Church letter details - only present for church requests',
    name: 'churchLetter',
    example: {
      url: 'https://res.cloudinary.com/.../church-letters/...',
      publicId: 'church-letters/...',
    },
    required: false,
  })
  churchLetter?: ChurchLetter;
}
