import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';

export class RequestAuditDto {
  @AutoMap()
  @ApiProperty()
  assigned: boolean;

  @AutoMap()
  @ApiProperty()
  assigner: string;

  @AutoMap()
  @ApiProperty()
  assigneeName: string;

  @AutoMap()
  @ApiProperty()
  assignee: number;

  @AutoMap()
  @ApiProperty()
  dateAssigned: Date;

  @AutoMap()
  @ApiProperty()
  completed: boolean;

  @AutoMap()
  @ApiProperty()
  completedDate: Date;

  @AutoMap()
  @ApiProperty()
  completedBy: string;

  @AutoMap()
  @ApiProperty()
  collected: boolean;

  @AutoMap()
  @ApiProperty()
  collectedDate: Date;

  @AutoMap()
  @ApiProperty()
  collectedBy: string;

  @AutoMap(() => ItemAuditDto)
  @ApiProperty({ type: ItemAuditDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ItemAuditDto)
  items: Array<ItemAuditDto>;
}
