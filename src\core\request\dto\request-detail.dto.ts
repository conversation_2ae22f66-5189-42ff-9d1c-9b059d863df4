import { AutoMap } from '@automapper/classes';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';
import { ApiProperty } from '@nestjs/swagger';
import { RequestAuditDto } from '@core/request/request-audit/dto/request-audit.dto';
import { ChurchLetter } from './type/church-letter.type';
import { RequestStatus } from '@common/enums/request-status.enum';

export class RequestDetailDto {
  @AutoMap()
  @ApiProperty()
  ministryName: string;

  @AutoMap()
  @ApiProperty()
  churchName: string;

  @AutoMap()
  @ApiProperty()
  requesterName: string;

  @AutoMap()
  @ApiProperty()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty()
  requesterPhone: string;

  @AutoMap()
  @ApiProperty()
  locationOfUse: string;

  @AutoMap()
  @ApiProperty()
  durationOfUse: Date;

  @AutoMap()
  @ApiProperty()
  dateOfCollection: Date;

  @AutoMap()
  @ApiProperty()
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty()
  descriptionOfRequest: string;

  @AutoMap()
  @ApiProperty({
    type: 'object',
    description: 'Church letter details - only present for church requests',
    name: 'churchLetter',
    example: {
      url: 'https://res.cloudinary.com/.../church-letters/...',
      publicId: 'church-letters/...',
    },
    required: false,
  })
  churchLetter?: ChurchLetter;

  @AutoMap()
  @ApiProperty({ enum: RequestStatus })
  requestStatus: RequestStatus;

  @AutoMap(() => RequestAuditDto)
  @ApiProperty({ type: RequestAuditDto })
  audit: RequestAuditDto;

  @AutoMap(() => ItemAuditDto)
  @ApiProperty({ type: ItemAuditDto, isArray: true })
  items: Array<ItemAuditDto>;
}
