import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddCollectionDateToRequestTable1753602121478
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'request',
      new TableColumn({
        name: 'date_of_collection',
        type: 'timestamp',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('request', 'date_of_collection');
  }
}
