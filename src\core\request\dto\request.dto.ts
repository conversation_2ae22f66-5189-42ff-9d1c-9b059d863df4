import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { RequestStatus } from '@common/enums/request-status.enum';
import { RequestAuditDto } from '@core/request/request-audit/dto/request-audit.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail } from 'class-validator';
import { ChurchLetter } from './type/church-letter.type';

export class RequestDto extends EntityDto {
  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The name of the requester',
    example: '<PERSON>',
    name: 'requesterName',
  })
  requesterName: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The email of the requester',
    name: 'requesterEmail',
    example: '<EMAIL>',
  })
  @IsEmail()
  requesterEmail: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The phone number of the requester',
    name: 'requester<PERSON><PERSON>',
    example: '+1234567890',
  })
  requesterPhone: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates if the requester is from a ministry',
    name: 'isMinistry',
  })
  isMinistry: boolean;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The name of the ministry if applicable',
    name: 'ministryName',
    example: 'Youth Ministry',
  })
  ministryName: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates if the requester is from a church',
    name: 'isChurch',
  })
  isChurch: boolean;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The name of the church if applicable',
    name: 'churchName',
    example: "St. Mary's Church",
  })
  churchName: string;

  @AutoMap()
  @ApiProperty({
    type: Number,
    description: "The ID of the requester's department",
    name: 'requesterDepartmentId',
    example: 1,
  })
  requesterDepartmentId: number;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: "The name of the requester's head of department",
    name: 'requesterHodName',
    example: 'Jane Smith',
  })
  @IsEmail()
  requesterHodName: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: "The email of the requester's head of department",
    name: 'requesterHodEmail',
    example: '<EMAIL>',
  })
  requesterHodEmail: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: "The phone number of the requester's head of department",
    name: 'requesterHodPhone',
    example: '+1234567890',
  })
  locationOfUse: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'The location where the item will be used',
    name: 'locationOfUse',
    example: 'Main Hall',
  })
  store: string;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'The duration for which the item will be used',
    name: 'durationOfUse',
    example: '2023-10-01T12:00:00Z',
  })
  durationOfUse: Date;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'The date when items requested are to be collected',
    name: 'dateOfCollection',
    example: '2023-10-01T12:00:00Z',
  })
  dateOfCollection: Date;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'The date when items requested are to be returned',
    name: 'dateOfReturn',
    example: '2023-10-01T12:00:00Z',
  })
  dateOfReturn: Date;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'A detailed description of the request',
    name: 'descriptionOfRequest',
    example: 'Requesting items for the upcoming event.',
  })
  descriptionOfRequest: string;

  @AutoMap()
  @ApiProperty({ enum: RequestStatus })
  requestStatus: RequestStatus;

  @AutoMap()
  @ApiProperty({
    type: 'object',
    description:
      'Church letter details (url and publicId) - only present for church requests',
    name: 'churchLetter',
    example: {
      url: 'https://res.cloudinary.com/.../church-letters/...',
      publicId: 'church-letters/...',
    },
    required: false,
  })
  churchLetter?: ChurchLetter;

  @AutoMap(() => RequestAuditDto)
  @ApiProperty({
    type: RequestAuditDto,
    description: 'Audit details of the request',
    name: 'audit',
  })
  audit: RequestAuditDto;
}
