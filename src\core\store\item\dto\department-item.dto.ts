import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';
import { Condition } from '@common/enums/condition.enum';
import { ItemUnitDto } from '@core/store/item-unit/dto/item-unit.dto';

export class DepartmentItemDto {
  @AutoMap(() => Number)
  @ApiProperty()
  id?: number;

  @AutoMap()
  @ApiProperty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  actualQuantity: number;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  availableQuantity: number;

  @AutoMap(() => Boolean)
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  fragile: boolean;

  @AutoMap(() => [ItemUnitDto])
  @ApiProperty()
  itemUnits: ItemUnitDto[];
}
